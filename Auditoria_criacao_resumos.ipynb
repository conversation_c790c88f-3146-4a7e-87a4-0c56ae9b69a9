{"cells": [{"cell_type": "markdown", "metadata": {"id": "1oq7b_dGUM-3"}, "source": ["# Notebook de auditoria de atendimentos usando IA\n", "O seguinte notebook usa-se da técnica `<PERSON><PERSON> as judge` e `self-reflection` para classificar e entender o comportamento de atendimentos. \\\n", "Logo em seguida a IA irá realizar a avaliação e trazer os resultados.  \n", "**Autor: <PERSON>.**\\\n", "**Versão: 2.0** \\\n", "\n", "\n", "Aqui está o referêncial teórico do que usamos hoje:\n", "Links: https://proceedings.neurips.cc/paper_files/paper/2023/hash/91f18a1287b398d378ef22505bf41832-Abstract-Datasets_and_Benchmarks.html \\\n", "https://arxiv.org/abs/2410.20774 \\\n", "https://arxiv.org/abs/2408.13006 \\\n", "\n", "\n", "**Sugestões de melhorias para uma v2**: \\\n", "O script atual possui uma GAP em reasoning, trazendo respostas em um modelo zero-shot, sendo que a literatura hoje mostra que precisamos de reasoning + few-shot (link abaixo)  \\\n", "Portanto, para uma possível v2, precisamos de uma estrutura multi agentes, em que cada um dos agente possui uma parte responsável do processo de auditoria, e esses gerem justificativas ou passo a passo de como estão atacando o problema proposto. \\\n", "Em cada agente, precisamos considerar a redução de tokens em prompt, assim podemos chamar uma LLM para cada regra ou n LLMs para a mesma regra. \\\n", "Para atacarmos os resumos, recomendo usar a LLM do Google, já que tem uma janela de contexto de 2MM tokens, aumentando a quantidade de atendimentos que conseguimos olhar.\n", "\n", "links: https://arxiv.org/abs/2209.01390 \\\n", "https://openreview.net/forum?id=yf1icZHC-l9 \\\n", "https://arxiv.org/abs/2212.09597  \n", "https://arxiv.org/abs/2402.18272\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "ZsL5WBdqUTGi"}, "source": ["# Instalando dependências"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3V8OEEb9UHS9", "outputId": "e778c241-5f9d-440f-9384-aa99e60134c9"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m41.2/41.2 kB\u001b[0m \u001b[31m1.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m297.2/297.2 kB\u001b[0m \u001b[31m7.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m8.9/8.9 MB\u001b[0m \u001b[31m83.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m140.1/140.1 kB\u001b[0m \u001b[31m11.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m33.9/33.9 MB\u001b[0m \u001b[31m48.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m14.0/14.0 MB\u001b[0m \u001b[31m79.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m85.3/85.3 kB\u001b[0m \u001b[31m6.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["! pip install openai anthropic tqdm litellm boto3 mysql-connector-python -q boto3"]}, {"cell_type": "markdown", "metadata": {"id": "YTgVNkSiUXOm"}, "source": ["# Importando dependências"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZI1wgTraUatT", "outputId": "9e6c4941-995c-423c-a690-d4481b7a0844"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.11/dist-packages/pydantic/_internal/_config.py:345: UserWarning: Valid config keys have changed in V2:\n", "* 'fields' has been removed\n", "  warnings.warn(message, UserWarning)\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import json\n", "import os\n", "from litellm import completion\n", "from tqdm import tqdm\n", "from google.colab import userdata\n", "from warnings import filterwarnings\n", "filterwarnings('ignore')"]}, {"cell_type": "markdown", "metadata": {"id": "UaUHxi3HVqTz"}, "source": ["# Lendo o resultado da auditoria\n", "Essa função talvez mude, já que os resultado vêm do Banco"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "dKGzM3REUgq9"}, "outputs": [], "source": ["import mysql.connector\n", "from mysql.connector import Error\n", "\n", "class DatabaseConnectionFactory:\n", "\n", "    def __init__(self, host, database, user, password):\n", "        self.config = {\n", "            \"host\": host,\n", "            \"database\": database,\n", "            \"user\": user,\n", "            \"password\": password\n", "        }\n", "\n", "    def create_connection(self):\n", "        try:\n", "            connection = mysql.connector.connect(**self.config)\n", "            if connection.is_connected():\n", "                print(f\"Conexão estabelecida com o banco {self.config['database']}\")\n", "                return connection\n", "        except Error as e:\n", "            print(f\"Erro ao conectar ao MySQL: {e}\")\n", "            return None\n", "\n", "class UserRepository:\n", "\n", "  def __init__(self, connection):\n", "      self.connection = connection\n", "\n", "  def get_conversation(self, fk_id_platform, result_type, id_criterion, start_date, end_date):\n", "      query = \"\"\"\n", "  SELECT acm.body, acm.direction, ac.id_chat  FROM  audit_chats ac\n", "  INNER JOIN audit_chat_messages acm ON ac.id_chat = acm.fk_id_chat\n", "  LEFT JOIN audit_rules_results arr ON ac.id_chat = arr.fk_id_chat\n", "  JOIN audit_rules ar ON arr.fk_id_rule = ar.id_rule\n", "  JOIN audit_action_criterion aac ON ar.fk_id_action_criterion = aac.id_action_criterion\n", "  JOIN audit_criterion acr ON aac.fk_id_criterion = acr.id_criterion\n", "  WHERE ac.fk_id_platform = %s and arr.value =  %s AND acr.id_criterion =%s AND ac.created_at BETWEEN %s AND %s\n", "  LIMIT 500\"\"\"\n", "      try:\n", "          cursor = self.connection.cursor()\n", "          cursor.execute(query, (fk_id_platform,result_type, id_criterion, start_date,end_date))\n", "          return cursor.fetchall()\n", "      except Error as e:\n", "          print(f\"Erro ao buscar usuários: {e}\")\n", "          return []\n", "\n", "  def get_criterion_results(self, fk_id_platform, id_criterion, start_date, end_date):\n", "    # definir a view para acessar\n", "    query = \"\"\"SELECT t.criterion_label, t.total_audit, t.total_valid,t.total_passed, t.total_failed,t.score_percentage\n", "              FROM dev_audit_gold.total_consolid_criterion t\n", "              WHERE id_platform = %s and id_criterion = %s and date BETWEEN %s and %s\n", "              ORDER BY date\"\"\"\n", "    try:\n", "        cursor = self.connection.cursor()\n", "        cursor.execute(query, (fk_id_platform, id_criterion, start_date,end_date))\n", "        return cursor.fetchall()\n", "    except Error as e:\n", "        print(f\"Erro ao buscar usuários: {e}\")\n", "        return []\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sS3gMDwXc8iu", "outputId": "291f1673-1295-4e48-d630-722fac58705f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Conexão estabelecida com o banco dev_audit_landzone\n"]}], "source": ["\n", "db_config = {\n", "        \"host\": \"dev-audta-ai.cluster-ro-cdkyomc4a6x4.us-east-1.rds.amazonaws.com\",\n", "        \"database\": \"dev_audit_landzone\",\n", "        \"user\": \"devdb\",\n", "        \"password\": \"jtNr6=LdR+R6aF4-d~J\"\n", "    }\n", "factory = DatabaseConnectionFactory(**db_config)\n", "connection = factory.create_connection()\n", "\n", "if connection:\n", "  try:\n", "      # Usar o repositório para realizar operações\n", "      user_repo = UserRepository(connection)\n", "\n", "      # Exemplo: Adicionar um usuário\n", "      negative_examples = user_repo.get_conversation(3, \"<PERSON>ão\",3 , \"2025-01-20 \" , \"2025-01-22\")\n", "      positive_examples = user_repo.get_conversation(3, \"Sim\", 3 , \"2025-01-20 \" , \"2025-01-22\")\n", "  finally:\n", "      connection.close()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jn2mb-7wBQgZ", "outputId": "4e1896c7-9d2d-4a12-e838-82e046ee8e02"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Conexão estabelecida com o banco dev_audit_gold\n"]}], "source": ["db_config = {\n", "        \"host\": \"dev-audta-ai.cluster-ro-cdkyomc4a6x4.us-east-1.rds.amazonaws.com\",\n", "        \"database\": \"dev_audit_gold\",\n", "        \"user\": \"devdb\",\n", "        \"password\": \"jtNr6=LdR+R6aF4-d~J\"\n", "    }\n", "factory = DatabaseConnectionFactory(**db_config)\n", "connection = factory.create_connection()\n", "\n", "if connection:\n", "  try:\n", "      # Usar o repositório para realizar operações\n", "      user_repo = UserRepository(connection)\n", "\n", "      # Exemplo: Adicionar um usuário\n", "      criterion_results_raw = user_repo.get_criterion_results(3,3 , \"2025-01-20 \" , \"2025-01-22\")\n", "  finally:\n", "      connection.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "eGBMerBCDTnc"}, "outputs": [], "source": ["positive_during_filter = 0\n", "negative_during_filter = 0\n", "total_during_filter = 0\n", "percentage_during_filter = 0\n", "# t.criterion_label, t.total_audit, t.total_valid,t.total_passed, t.total_failed,t.score_percentage\n", "for name, total,total_valid, total_passed, total_negative, score in criterion_results_raw:\n", "  total_during_filter += total_valid\n", "  negative_during_filter += total_negative\n", "  positive_during_filter += total_passed\n", "\n", "percentagem_during_filter = round((positive_during_filter/total_during_filter)*100,2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "EqSApLO2D1v3"}, "outputs": [], "source": ["criterion_result_string = f\"Criterio: {criterion_results_raw[0][0]} Total valido: {total_valid} Positivos: {positive_during_filter} Negativos: {negative_during_filter} Total: {total_during_filter} Porcentagem: {percentagem_during_filter}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "KrPmu2Nrp1Zf"}, "outputs": [], "source": ["negative_ids = []\n", "positive_ids = []\n", "for mensagem, direction, id in negative_examples:\n", "  negative_ids.append(id)\n", "for mensagem, direction, id in positive_examples:\n", "  positive_ids.append(id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hbYd3xxGp5xs"}, "outputs": [], "source": ["\n", "import random\n", "\n", "ids_selecionados_negativos= random.sample(negative_ids,15)\n", "ids_selecionados_positivos= random.sample(positive_ids,15)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jobRTfOkeO4P"}, "outputs": [], "source": ["negative_conversation = \"\"\n", "positive_conversation = \"\"\n", "for mensagem, direction, id in negative_examples:\n", "  if id in ids_selecionados_negativos:\n", "    negative_conversation += f\"ID: {id}. {direction}: {mensagem}\\n\"\n", "\n", "for mensagem,direction,id in positive_examples:\n", "  if id in ids_selecionados_positivos:\n", "    positive_conversation += f\"ID: {id}. {direction}: {mensagem}\\n\""]}, {"cell_type": "markdown", "metadata": {"id": "QYpZtpTmWuWQ"}, "source": ["# Caso precise de tratamento a função está aqui"]}, {"cell_type": "markdown", "metadata": {"id": "ztTzWwYqXLpf"}, "source": ["# API Keys auditoria"]}, {"cell_type": "code", "source": ["import boto3, json\n", "\n", "def get_secret():\n", "    secret_name = \"prod/audie\"\n", "    region_name = \"us-east-1\"\n", "\n", "    client = boto3.client(\"secretsmanager\",\n", "                          region_name=region_name,\n", "                          aws_secret_access_key=\"Ks3MM/R+FLxEe+kCLkRw4CEszeCO6T+Km1ZNOH+P\",\n", "                          aws_access_key_id=\"********************\")\n", "    resp = client.get_secret_value(SecretId=secret_name)\n", "    return json.loads(resp[\"SecretString\"])\n", "\n", "secrets = get_secret()"], "metadata": {"id": "Gtrooolm2cph"}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "8ID2xH-CXLHH", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "d7102c90-86be-4b78-f875-596a330dab28"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{'OPENAI_API_KEY': '***********************************************************************************************************************************************************************'}\n"]}], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = secrets[\"OPENAI_API_KEY\"]\n", "os.environ[\"AWS_ACCESS_KEY_ID\"] = \"********************\"\n", "os.environ[\"AWS_SECRET_ACCESS_KEY\"] = \"0P0qWQTNwgESYyrq+8bsXkfYsK2G07niMi7CvrWB\"\n", "os.environ[\"AWS_REGION_NAME\"] = \"us-east-1\"\n", "os.environ[\"DEEPSEEK_API_KEY\"] = \"***********************************\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "uF1soOMDwKAg"}, "outputs": [], "source": ["from collections import defaultdict\n", "from decimal import Decimal\n", "from typing import List\n", "\n", "def process_criterion_results(criterion_results_raw: List[tuple]) -> str:\n", "    # Dicionário para armazenar os resultados agregados por critério\n", "    aggregated_results = defaultdict(lambda: {\"total\": 0, \"total_valid\": 0, \"total_passed\": 0, \"total_negative\": 0})\n", "\n", "    # Agrega os valores para critérios repetidos\n", "    for criterion_name, total, total_valid, total_passed, total_negative, score in criterion_results_raw:\n", "        aggregated_results[criterion_name][\"total\"] += total\n", "        aggregated_results[criterion_name][\"total_valid\"] += total_valid\n", "        aggregated_results[criterion_name][\"total_passed\"] += total_passed\n", "        aggregated_results[criterion_name][\"total_negative\"] += total_negative\n", "\n", "    # Inicializa a string para os resultados finais\n", "    results = []\n", "\n", "    # Processa os critérios agregados\n", "    for criterion_name, values in aggregated_results.items():\n", "        total_valid = values[\"total_valid\"]\n", "        total_passed = values[\"total_passed\"]\n", "        total_negative = values[\"total_negative\"]\n", "\n", "        # Calcula a porcentagem de sucesso\n", "        if total_valid > 0:\n", "            percentage_during_filter = round((total_passed / total_valid) * 100, 2)\n", "        else:\n", "            percentage_during_filter = 0.0\n", "\n", "        # Monta o texto formatado\n", "        result = (f\"Criterio: {criterion_name} \"\n", "                  f\"Total Valido: {total_valid} \"\n", "                  f\"Positivos: {total_passed} \"\n", "                  f\"Negativos: {total_negative} \"\n", "                  f\"Porcentagem: {percentage_during_filter}%\")\n", "\n", "        results.append(result)\n", "\n", "    # Retorna os resultados como uma string unificada\n", "    return \"\\n\".join(results)\n"]}, {"cell_type": "code", "source": [], "metadata": {"id": "XZvls3K-2Wix"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "u7r3mTM1XRcl"}, "source": ["# Função para conectar com as llms"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mlGe5s5qW5xw"}, "outputs": [], "source": ["\n", "def chat_completion_request(system,messages: list ,model) :\n", "    try:\n", "\n", "      if \"claude\" in model:\n", "\n", "        response = client.messages.create(\n", "            max_tokens=10000,\n", "            system  = system,\n", "            messages=messages,\n", "            model=model,\n", "            temperature = 0,\n", "            top_p = 0,\n", "        )\n", "        raw_response = response.content[0].text\n", "\n", "        return raw_response, response.usage\n", "\n", "      else :\n", "        response =completion(\n", "              messages=messages,\n", "              model= \"bedrock/llama/arn:aws:bedrock:us-east-1:************:imported-model/ta1av12mu9bp\"\n", "          )\n", "        print(response)\n", "        return response.choices[0].message.content, response.usage\n", "\n", "    except Exception as e:\n", "        print(\"Unable to generate ChatCompletion response\")\n", "        print(f\"Exception: {e}\")\n", "        return e\n"]}, {"cell_type": "markdown", "metadata": {"id": "2J3ODNvKXYdC"}, "source": ["# <PERSON>ando os resumos"]}, {"cell_type": "markdown", "metadata": {"id": "w6btGUFCZUzZ"}, "source": ["## <PERSON><PERSON><PERSON> o prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4ncfmR9xatjH"}, "outputs": [], "source": ["system = \"\"\"Você é um auditor de conversas de atendimento cujo objetivo é avaliar O nível de satisfação do cliente durante a interação foi identificado e está evidente se ele ficou satisfeito com as respostas e soluções apresentadas?. Nesta auditoria, o resultado obtido foi:\n", "{{resultado}}\n", "Objetivo\n", "\t•\tGerar um resumo dos 4 principais aspectos (positivos ou negativos) relacionados ao Risco de Cancelamento, cada um com:\n", "\t1.\tDescrição objetiva do que ocorreu.\n", "\t2.\tImpacto no negócio.\n", "\t3.\tIDs das mensagens que evidenciam o aspecto.\n", "\t•\tAo final, fornecer 3 recomendações de melhoria ou manutenção de boas práticas, também fazendo referência aos IDs (quando relevantes).\n", "\n", "Requisitos de Formatação\n", "\t1.\tA resposta deve estar em formato JSON.\n", "\t2.\tNão utilizar expressões vagas como “em várias conversas” ou “diversos clientes”; use as informações constantes em {resultado} (percentuais, quantidades, etc.).\n", "\t3.\tCite exemplos de IDs para cada aspecto\n", "\t4.\tCada aspecto deve ser uma chave diferente dentro de \"resumo\" (por exemplo, \"Risco de Cancelamento\", \"Dificuldades Técnicas no Site\", \"Preços e Descontos Como Decisores\",  \"Boa capacidade de argumentação\").\n", "\t5.\t<PERSON><PERSON><PERSON> os textos curtos e objetivos.\n", "\n", "Exemplo de Formato de Saída (para referência):\n", "{\n", "  \"resumo\": {\n", "    \"Risco de Cancelamento\": \"Com base nos dados, observou-se que parte dos clientes manifestou intenção de cancelar ou desistir das compras, devido a fatores como taxas de juros elevadas (ID [inserir o id aqui]) e dificuldades técnicas (ID [inserir o id aqui]).\",\n", "    \"Dificuldades Técnicas no Site\": \"Falhas no sistema, como problemas na finalização de compra (ID [inserir o id aqui]) e a perda de produtos no carrinho (ID [inserir o id aqui]), levaram clientes a desistirem da compra, impactando as vendas.\",\n", "    \"Preços e Descontos Como Decisores\": \"Alguns clientes, como no ID [inserir o id aqui] e ID [inserir o id aqui], se queixaram de preços acima do esperado mesmo após descontos, resultando em desistência.\",\n", "\"Boa argumentação\" : \"Apesar das adversidades, os Agentes conseguiram convencer o cliente na maioria dos casos com cupons, pneus mais baratos que oferecem a mesma qualidade\"\n", "  },\n", "  \"recomendacoes\": {\n", "    \"Melhoria no Processo de Compra\": \"Corrigir instabilidades técnicas e otimizar o fluxo de checkout para reduzir abandonos.\",\n", "    \"Revisão de Estratégias de Preço\": \"Avaliar as políticas de preço e cupons para garantir que os clientes percebam vantagens reais.\",\n", "    \"Treinamento de Atendimento\": \"Capacitar agentes para lidar com objeções sobre preço e juros, oferecendo soluções personalizadas que reduzam o risco de cancelamento.\"\n", "  }\n", "}\n", "\n", "Instrução Final\n", "\t•\tEscreva o output exatamente em formato JSON, com as chaves \"resumo\" e \"recomendacoes\".\n", "\t•\tCada chave em \"resumo\" deve conter uma única string explicativa.\n", "\t•\tNão use frases como “em várias conversas”; seja objetivo.\n", " \"\"\"\n", "user = \"\"\"\n", "Conversas ruins: {conversas_ruins}\n", "\n", "###\n", "\n", "Conversas boas : {conversas_boas}\n", "\n", "\n", "\"\"\""]}, {"cell_type": "markdown", "metadata": {"id": "uzakJn3HYQpH"}, "source": ["## Função para criar message_array no formato da OpenAI"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "W-sD39WKYQV5"}, "outputs": [], "source": ["\n", "def create_message_array_summary(prompt, audit_metrics, criterion_name, user, standard_conversation, bad_conversation, model):\n", "  if \"claude\" not in model :\n", "    message_array = []\n", "    # message_array.append({\"role\": \"system\", \"content\": prompt.replace(\"{{nome_criterio}}\", criterion_name).replace(\"{{resultado}}\", audit_metrics)})\n", "    message_array.append({\"role\": \"user\", \"content\":prompt.replace(\"{{nome_criterio}}\", criterion_name).replace(\"{{resultado}}\", audit_metrics) +  user.format(conversas_boas=standard_conversation, conversas_ruins= bad_conversation)})\n", "    return message_array, \"\"\n", "\n", "  else:\n", "    message_array = []\n", "    system = prompt.replace(\"{{nome_criterio}}\", criterion_name).replace(\"{{resultado}}\", audit_metrics)\n", "    message_array.append({\"role\": \"user\", \"content\": user.format(standard_conversation=standard_conversation, bad_conversation=bad_conversation)})\n", "    return message_array, system"]}, {"cell_type": "markdown", "metadata": {"id": "ev_nST5NIO2c"}, "source": ["## <PERSON><PERSON><PERSON> os resultados"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Z_G_i0WWXdSN", "outputId": "2f7b1ad2-ed8d-4b1d-970e-d255752608e5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ModelResponse(id='chatcmpl-cdf3c026-cdc6-4492-a37d-3eb9a3f17bfc', created=1738591016, model='llama/arn:aws:bedrock:us-east-1:************:imported-model/ta1av12mu9bp', object='chat.completion', system_fingerprint=None, choices=[Choices(finish_reason='stop', index=0, message=Message(content='###\\n\\nConversas ruins: ID: 182256. Cliente: Boa nt\\nID: 182256. Cliente: Boa tarde\\nID: 182256. Agente: Ol<PERSON>, eu sou a Lory e darei continuidade no seu atendimento. 🤗 Com o que posso te ajudar?\\nID: 182256. Cliente: Olá\\nID: 182256. Agente: em que posso te ajudar, <PERSON>?\\nID: 182256. Cliente: Gostaria de saber exatamente como funciona o plano start\\nID: 182256. Cliente: Pois adquiri mais não estou entendendo\\nID: 182256. Cliente: Ond será descontado?\\nID: 182256. Cliente: Fui tentar fazer recarga \\nEstá me cobrando taxa\\nID: 182256. Cliente: Falaram q esse plano não tem taxa\\nID: 182256. Cliente: Pod mandar áudio explicando\\nID: 182256. Agente: None\\nID: 182256. Agente: None\\nID: 182256. Agente: None\\nID: 182256. Agente: None\\nID: 182256. Agente: ficou alguma dúvida?\\nID: 182256. Cliente: Nenhuma\\nID: 182256. Cliente: Agirá entendi tudo\\nID: 182256. Cliente: MT obrigada\\nID: 182256. Cliente: Tem mais uma coisa\\nID: 182256. Cliente: Estava fazendo um empréstimo...\\nID: 182256. Cliente: Porém na hora de colocar o banco \\nNão tinha o meu banco nas alternativas\\nID: 182256. Cliente: O meu é banco on line\\nID: 182256. Agente: Nessa outra questão não consigo te auxiliar, Simone\\nID: 182256. Agente: somente sobre o plano mesmo\\nID: 182256. Agente: como não restou dúvidas irei encerrar nosso atendimento por aqui\\nID: 182256. Agente: Muito obrigada por sua atenção! 🤗Seu atendimento nesse canal será finalizado e em breve você receberá um link para avaliar o meu atendimento. Lembrando que estamos disponíveis em todos os canais de', role='assistant', tool_calls=None, function_call=None, provider_specific_fields=None))], usage=Usage(completion_tokens=512, prompt_tokens=24619, total_tokens=25131, completion_tokens_details=None, prompt_tokens_details=None))\n"]}], "source": ["criterion_models = {\"Sentimento e Emoção: Satisfação\": \"us.anthropic.claude-3-5-haiku-20241022-v1:0\" }\n", "criterion_summaries = []\n", "summaries_cost = []\n", "criterion_name = \"AVALIAR TELA\"\n", "\n", "model = \"deepseek/deepseek-chat\"\n", "\n", "if \"claude\" in model:\n", "  array_of_messages, system = create_message_array_summary(system, criterion_result_string, criterion_name, user, positive_conversation, negative_conversation, model)\n", "\n", "  response, usage = chat_completion_request(system, array_of_messages,model)\n", "else:\n", "\n", "  array_of_messages, system = create_message_array_summary(system, criterion_result_string, criterion_name, user, positive_conversation, negative_conversation, model)\n", "  response, usage = chat_completion_request(\"\",array_of_messages, model )\n", "\n", "criterion_summaries.append(response)\n", "summaries_cost.append(usage)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 122}, "id": "v45Y2uM_iVMm", "outputId": "95c6c1b5-ec2c-48f3-f043-0ad8b4193d89"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'###\\n\\nConversas ruins: ID: 182256. Cliente: Boa nt\\nID: 182256. Cliente: <PERSON><PERSON> tarde\\nID: 182256. Agente: <PERSON><PERSON><PERSON>, eu sou a Lory e darei continuidade no seu atendimento. 🤗 Com o que posso te ajudar?\\nID: 182256. Cliente: Olá\\nID: 182256. Agente: em que posso te ajudar, <PERSON>?\\nID: 182256. Cliente: Gostaria de saber exatamente como funciona o plano start\\nID: 182256. Cliente: Pois adquiri mais não estou entendendo\\nID: 182256. Cliente: Ond será descontado?\\nID: 182256. Cliente: Fui tentar fazer recarga \\nEstá me cobrando taxa\\nID: 182256. Cliente: Falaram q esse plano não tem taxa\\nID: 182256. Cliente: Pod mandar áudio explicando\\nID: 182256. Agente: None\\nID: 182256. Agente: None\\nID: 182256. Agente: None\\nID: 182256. Agente: None\\nID: 182256. Agente: ficou alguma dúvida?\\nID: 182256. Cliente: Nenhuma\\nID: 182256. Cliente: Agirá entendi tudo\\nID: 182256. Cliente: MT obrigada\\nID: 182256. Cliente: Tem mais uma coisa\\nID: 182256. Cliente: Estava fazendo um empréstimo...\\nID: 182256. Cliente: Porém na hora de colocar o banco \\nNão tinha o meu banco nas alternativas\\nID: 182256. Cliente: O meu é banco on line\\nID: 182256. Agente: Nessa outra questão não consigo te auxiliar, Simone\\nID: 182256. Agente: somente sobre o plano mesmo\\nID: 182256. Agente: como não restou dúvidas irei encerrar nosso atendimento por aqui\\nID: 182256. Agente: Muito obrigada por sua atenção! 🤗Seu atendimento nesse canal será finalizado e em breve você receberá um link para avaliar o meu atendimento. Lembrando que estamos disponíveis em todos os canais de'"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["response"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DW2DGc0po3wf", "outputId": "bd55478a-c517-4f7d-8d57-a82086d299f9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Expecting value: line 1 column 1 (char 0)\n"]}], "source": [" # Please install OpenAI SDK first: `pip3 install openai`\n", "\n", "from openai import OpenAI\n", "\n", "client = OpenAI(api_key=os.environ[\"DEEPSEEK_API_KEY\"], base_url=\"https://api.deepseek.com\")\n", "try:\n", "  response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"\"\"resuma a seguinte conversa trazendo os pontos principais e insights ou melhorias que poderiam acontecer: Olá, tudo bem? Aqui é Isa, do banco BV.😊 Vi que você não tem utilizado seu cartão de crédito. Tem uns minutinhos para conversarmos e descobrir como podemos oferecer mais vantagens e benefícios para você?💙\"\n", "Bom dia!\n", "\"Bom dia! 😊 Você sabia que os cartões BV oferecem diversos benefícios que podem variar de acordo com o seu tipo de cartão? Temos Assistência veicular e Residencial gratuitas, você pode acumular pontos BV Merece ou cashback em todas as suas compras e ainda conta com diversos descontos incríveis nos nossos parceiros BV! Incrível, não é mesmo? Você consegue retomar o uso do seu cartão nos próximos dias para manter os seus benefícios ativos?\"\n", "Ok\n", "\"Que ótimo! 🎉 Você consegue retomar o uso do seu cartão nos próximos dias? Assim, você pode aproveitar todos os benefícios que ele oferece! Se precisar de alguma ajuda, estou aqui! 💙\"\n", "Certo\n", "\"Estou encerrando o nosso atendimento por aqui e qualquer dúvida continuo a disposição!  Você vai receber um link para me avaliar. <PERSON><PERSON> obrigada pela sua atenção, tenha um ótimo dia!\"\n", "\"QUELIANE, tenha desconto em lojas parceiras do BV e economize na hora de presentear!🎁 Acesse: https://www.bv.com.br/cartao/cupons\"\n", "\"QUELIANE, continuo à disposição para falarmos sobre o seu cartão BV.😉 Podemos retomar o atendimento?\"\n", "\"olte a usar seu cartão de crédito BV e aproveite benefícios como cashback, descontos exclusivos e programas de recompensas. Acesse o nosso app e fique por dentro de tudo que o seu cartão BV te oferece: https://appbv.bv.com.br/abrirappbv/08COP Se precisar de mais informações ou tiver qualquer dúvida, estou aqui para ajudar!😊\"\n", "\"\"\"},\n", "    ]\n", ")\n", "  print(response)\n", "except Exception as e:\n", "  print(e)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "2-Sr6jwzhT7U"}, "outputs": [], "source": ["response"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "T3pDuhB4if2t"}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {"id": "wauvsEL7IUao"}, "source": ["## <PERSON><PERSON><PERSON> respostas"]}, {"cell_type": "markdown", "metadata": {"id": "YQN6-TKlDfAD"}, "source": ["# Resumo Geral"]}, {"cell_type": "markdown", "metadata": {"id": "Jx1lqxivFTaY"}, "source": ["## <PERSON><PERSON><PERSON> o prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bJkAZQwIDZBy"}, "outputs": [], "source": ["prompt_geral = client_bedrock.get_prompt(promptIdentifier=\"resumo-geral-auditoria\")\n", "system_geral = prompt_geral[\"variants\"][\"templateConfiguration\"][\"system\"][0][\"text\"]\n", "user_geral = prompt_geral[\"variants\"][\"templateConfiguration\"][\"content\"][0][\"text\"]"]}, {"cell_type": "markdown", "metadata": {"id": "xtWB2lcOIgbr"}, "source": ["## Agrupando os resultados da auditoria"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "zg31ckyiHQpX"}, "outputs": [], "source": ["resumo_auditoria = '\\n'.join(resultado_auditoria_criterio)"]}, {"cell_type": "markdown", "metadata": {"id": "a4BmGIPsFmPR"}, "source": ["## <PERSON><PERSON>do os resumos"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "tBF5g5XxFllG"}, "outputs": [], "source": ["message_array = [{\"role\": \"user\", \"content\": user_geral.replace(\"{{resumo_auditoria}}\", criterion_summaries)}]\n", "response, usage = chat_completion_request(system_geral.replace(\"{{abstract}}\", resumo_auditoria), message_array)"]}, {"cell_type": "markdown", "metadata": {"id": "AqRMGFykIXjI"}, "source": ["## <PERSON><PERSON><PERSON> resposta"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bAD3f0aTIEx3"}, "outputs": [], "source": ["with open(\"resumo_geral.txt\", \"w\") as f:\n", "    f.write(response)"]}], "metadata": {"colab": {"provenance": [], "collapsed_sections": ["u7r3mTM1XRcl", "w6btGUFCZUzZ", "uzakJn3HYQpH", "ev_nST5NIO2c"]}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}